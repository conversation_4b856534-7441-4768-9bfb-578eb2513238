import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/providers/auth_provider.dart';

import 'package:culture_connect/widgets/experience_card.dart';
import 'package:shimmer/shimmer.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with SingleTickerProviderStateMixin {
  final _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isLoading = true;
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    _animationController.forward();
    _simulateLoading();
  }

  Future<void> _simulateLoading() async {
    await Future.delayed(const Duration(seconds: 2));
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: _isLoading ? _buildLoadingState() : _buildCurrentScreen(),
    );
  }

  Widget _buildLoadingState() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Container(
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(height: 24),
              Container(
                height: 180,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              const SizedBox(height: 32),
              Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentScreen() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildHomeScreen(),
    );
  }

  Widget _buildHomeScreen() {
    return RefreshIndicator(
      onRefresh: () async {
        setState(() {
          _isLoading = true;
        });
        await _simulateLoading();
      },
      color: AppTheme.primaryColor,
      backgroundColor: Colors.white,
      child: SingleChildScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),

              // Welcome Section with Animation
              _buildWelcomeSection(),

              const SizedBox(height: 24),

              // Enhanced Search Bar
              _buildEnhancedSearchBar(),

              const SizedBox(height: 24),

              // Hero Section with Parallax Effect
              _buildHeroSection(),

              const SizedBox(height: 32),

              // Quick Actions with Modern Design
              _buildQuickActionsSection(),

              const SizedBox(height: 32),

              // Explore by Category Section
              _buildExploreByCategorySection(),

              const SizedBox(height: 32),

              // Featured Experiences with Enhanced Cards
              _buildFeaturedExperiencesSection(),

              const SizedBox(height: 32),

              // Top Guides with AirBnB-style Cards
              _buildTopGuidesSection(),

              const SizedBox(height: 32),

              // Nearby Experiences with Map Preview
              _buildNearbyExperiencesSection(),

              const SizedBox(height: 100),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final user = ref.watch(authProvider);
    final userName =
        user?.displayName ?? user?.email?.split('@')[0] ?? 'Explorer';

    return Row(
      children: [
        // Flat Design Hamburger Menu
        Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: AppTheme.outline,
              width: 1.5,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              onTap: () {
                // TODO: Open drawer/menu
              },
              child: const Icon(
                Icons.menu_rounded,
                color: AppTheme.textPrimaryColor,
                size: 20,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        // Simplified Name Display
        Expanded(
          child: Text(
            'Hi $userName',
            style: const TextStyle(
              color: AppTheme.textPrimaryColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const Spacer(),
        // Flat Design Notification Icon
        Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            border: Border.all(
              color: AppTheme.outline,
              width: 1.5,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
              onTap: () {
                // TODO: Navigate to notifications
              },
              child: Stack(
                children: [
                  const Center(
                    child: Icon(
                      Icons.notifications_outlined,
                      color: AppTheme.textPrimaryColor,
                      size: 20,
                    ),
                  ),
                  // Flat notification badge
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: AppTheme.errorColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedSearchBar() {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusXXLarge),
        border: Border.all(
          color: AppTheme.outline,
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.only(
                left: AppTheme.spacingLarge, right: AppTheme.spacingMedium),
            child: Icon(
              Icons.search_rounded,
              color: AppTheme.textSecondaryColor,
              size: 20,
            ),
          ),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: const TextStyle(
                fontSize: 16,
                color: AppTheme.textPrimaryColor,
                fontWeight: FontWeight.w400,
              ),
              decoration: InputDecoration(
                hintText: 'Search destinations, experiences...',
                hintStyle: TextStyle(
                  color: AppTheme.textSecondaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged: (value) {
                setState(() {});
              },
              onTap: () {
                // TODO: Navigate to search screen
              },
            ),
          ),
          // Flat Filter Icon
          Container(
            margin: const EdgeInsets.only(right: AppTheme.spacingSmall),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusXLarge),
                onTap: () {
                  // TODO: Show filter options
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusXLarge),
                  ),
                  child: const Icon(
                    Icons.tune_rounded,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeroSection() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        border: Border.all(
          color: AppTheme.outline,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        child: Stack(
          children: [
            // High-quality travel/culture background image
            Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(
                    'https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80',
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // Subtle overlay for text readability
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.black.withAlpha(51), // 0.2 opacity
                    Colors.transparent,
                    Colors.black.withAlpha(102), // 0.4 opacity
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),

            // Content
            Positioned(
              left: 24,
              bottom: 24,
              right: 24,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'The Soul of Travel, Guided by AI',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      height: 1.2,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 3,
                          color: Colors.black26,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Discover authentic cultural experiences worldwide',
                    style: TextStyle(
                      color: Colors.white.withAlpha(240),
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      shadows: const [
                        Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black26,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            children: [
              _buildModernQuickLink(
                icon: Icons.flight_takeoff_rounded,
                label: 'Flights',
                backgroundColor: const Color(0xFF4A90E2),
                onTap: () =>
                    Navigator.pushNamed(context, '/travel/flights/search'),
              ),
              _buildModernQuickLink(
                icon: Icons.hotel_rounded,
                label: 'Hotels',
                backgroundColor: const Color(0xFF50C878),
                onTap: () => Navigator.pushNamed(context, '/travel/hotels'),
              ),
              _buildModernQuickLink(
                icon: Icons.restaurant_rounded,
                label: 'Restaurants',
                backgroundColor: const Color(0xFFFF6B6B),
                onTap: () =>
                    Navigator.pushNamed(context, '/travel/restaurants'),
              ),
              _buildModernQuickLink(
                icon: Icons.local_taxi_rounded,
                label: 'Transport',
                backgroundColor: const Color(0xFFFFD93D),
                onTap: () => Navigator.pushNamed(context, '/travel/transport'),
              ),
              _buildModernQuickLink(
                icon: Icons.explore_rounded,
                label: 'Explore',
                backgroundColor: const Color(0xFF9B59B6),
                onTap: () => Navigator.pushNamed(context, '/explore'),
              ),
              _buildModernQuickLink(
                icon: Icons.translate_rounded,
                label: 'Translate',
                backgroundColor: const Color(0xFF1ABC9C),
                onTap: () => Navigator.pushNamed(context, '/voice-translation'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernQuickLink({
    required IconData icon,
    required String label,
    required Color backgroundColor,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 90,
      margin: const EdgeInsets.only(right: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: backgroundColor.withAlpha(26), // Light fill
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusLarge),
                    border: Border.all(
                      color: backgroundColor,
                      width: 2, // Pronounced border
                    ),
                  ),
                  child: Center(
                    child: Icon(
                      icon,
                      color: backgroundColor,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExploreByCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Explore by category',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 50,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            children: [
              _buildCategoryPill(
                title: 'Cultural Tours',
                icon: Icons.account_balance_rounded,
                backgroundColor: AppTheme.primaryColor,
                isSelected: true,
                onTap: () =>
                    Navigator.pushNamed(context, '/explore?category=cultural'),
              ),
              _buildCategoryPill(
                title: 'Food & Dining',
                icon: Icons.restaurant_rounded,
                backgroundColor: const Color(0xFFFF6B6B),
                isSelected: false,
                onTap: () =>
                    Navigator.pushNamed(context, '/explore?category=food'),
              ),
              _buildCategoryPill(
                title: 'Art & Craft',
                icon: Icons.palette_rounded,
                backgroundColor: const Color(0xFF4ECDC4),
                isSelected: false,
                onTap: () =>
                    Navigator.pushNamed(context, '/explore?category=art'),
              ),
              _buildCategoryPill(
                title: 'Music & Dance',
                icon: Icons.music_note_rounded,
                backgroundColor: const Color(0xFFFFE66D),
                isSelected: false,
                onTap: () =>
                    Navigator.pushNamed(context, '/explore?category=music'),
              ),
              _buildCategoryPill(
                title: 'Nature & Wildlife',
                icon: Icons.nature_rounded,
                backgroundColor: const Color(0xFF10B981),
                isSelected: false,
                onTap: () =>
                    Navigator.pushNamed(context, '/explore?category=nature'),
              ),
              _buildCategoryPill(
                title: 'Adventure',
                icon: Icons.hiking_rounded,
                backgroundColor: const Color(0xFFFF8C00),
                isSelected: false,
                onTap: () =>
                    Navigator.pushNamed(context, '/explore?category=adventure'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryPill({
    required String title,
    required IconData icon,
    required Color backgroundColor,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.borderRadiusRounded),
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? backgroundColor : AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusRounded),
              border: Border.all(
                color: isSelected ? backgroundColor : AppTheme.outline,
                width: 1.5,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  color: isSelected ? Colors.white : backgroundColor,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color:
                        isSelected ? Colors.white : AppTheme.textPrimaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedExperiencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Featured Experiences',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/explore');
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: _buildOptimizedFeaturedExperiencesList(),
        ),
      ],
    );
  }

  // Optimized: Featured experiences with lazy loading and const data
  Widget _buildOptimizedFeaturedExperiencesList() {
    // Enhanced sample data with high-quality images and diverse experiences
    const featuredExperiences = [
      {
        'title': 'Traditional Japanese Tea Ceremony',
        'location': 'Kyoto, Japan',
        'imageUrl':
            'https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2041&q=80',
        'rating': 4.9,
        'price': '\$65',
        'isFeatured': true,
        'category': 'Cultural',
        'reviewCount': 342,
      },
      {
        'title': 'Moroccan Cooking Workshop',
        'location': 'Marrakech, Morocco',
        'imageUrl':
            'https://images.unsplash.com/photo-1539136788836-5699e78bfc75?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
        'rating': 4.8,
        'price': '\$55',
        'isFeatured': true,
        'category': 'Culinary',
        'reviewCount': 287,
      },
      {
        'title': 'Flamenco Dance Experience',
        'location': 'Seville, Spain',
        'imageUrl':
            'https://images.unsplash.com/photo-1518834107812-67b0b7c58434?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2035&q=80',
        'rating': 4.7,
        'price': '\$45',
        'isFeatured': false,
        'category': 'Dance',
        'reviewCount': 156,
      },
      {
        'title': 'Balinese Art & Craft Workshop',
        'location': 'Ubud, Bali',
        'imageUrl':
            'https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
        'rating': 4.6,
        'price': '\$40',
        'isFeatured': false,
        'category': 'Art',
        'reviewCount': 198,
      },
      {
        'title': 'Aboriginal Dreamtime Stories',
        'location': 'Alice Springs, Australia',
        'imageUrl':
            'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
        'rating': 4.8,
        'price': '\$50',
        'isFeatured': true,
        'category': 'Cultural',
        'reviewCount': 124,
      },
    ];

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      itemCount: featuredExperiences.length,
      // Optimized: Add caching for better performance
      cacheExtent: 1000,
      itemBuilder: (context, index) {
        final experience = featuredExperiences[index];
        return Container(
          width: 240,
          margin: EdgeInsets.only(
            right: index < featuredExperiences.length - 1 ? 16 : 0,
          ),
          child: ExperienceCard(
            title: experience['title'] as String,
            location: experience['location'] as String,
            imageUrl: experience['imageUrl'] as String,
            rating: experience['rating'] as double,
            price: experience['price'] as String,
            isFeatured: experience['isFeatured'] as bool,
            category: experience['category'] as String,
            reviewCount: experience['reviewCount'] as int,
            onTap: () {
              // TODO: Navigate to experience details
            },
          ),
        );
      },
    );
  }

  Widget _buildTopGuidesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Top Local Guides',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/guides');
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            itemCount: 4,
            itemBuilder: (context, index) {
              final guides = [
                {
                  'name': 'Adebayo O.',
                  'location': 'Lagos',
                  'imageUrl': 'https://randomuser.me/api/portraits/men/32.jpg',
                  'rating': 4.9,
                  'specialties': ['Culinary', 'History'],
                },
                {
                  'name': 'Wanjiku M.',
                  'location': 'Nairobi',
                  'imageUrl':
                      'https://randomuser.me/api/portraits/women/44.jpg',
                  'rating': 4.8,
                  'specialties': ['Wildlife', 'Culture'],
                },
                {
                  'name': 'Thabo N.',
                  'location': 'Cape Town',
                  'imageUrl': 'https://randomuser.me/api/portraits/men/22.jpg',
                  'rating': 4.7,
                  'specialties': ['Wine', 'Tours'],
                },
                {
                  'name': 'Amara C.',
                  'location': 'Accra',
                  'imageUrl':
                      'https://randomuser.me/api/portraits/women/67.jpg',
                  'rating': 4.9,
                  'specialties': ['Art', 'Music'],
                },
              ];

              final guide = guides[index];
              return Container(
                margin: EdgeInsets.only(
                  right: index < guides.length - 1 ? 20 : 0,
                ),
                child: _buildEnhancedGuideItem(
                  name: guide['name'] as String,
                  location: guide['location'] as String,
                  imageUrl: guide['imageUrl'] as String,
                  rating: guide['rating'] as double,
                  specialties: guide['specialties'] as List<String>,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedGuideItem({
    required String name,
    required String location,
    required String imageUrl,
    required double rating,
    required List<String> specialties,
  }) {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Profile image
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: DecorationImage(
                  image: NetworkImage(imageUrl),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Info section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 12,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 2),
                      Expanded(
                        child: Text(
                          location,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.star,
                        size: 12,
                        color: Colors.amber,
                      ),
                      const SizedBox(width: 2),
                      Text(
                        rating.toString(),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNearbyExperiencesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Nearby Experiences',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to explore screen with location filter
              },
              child: const Text(
                'View All',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ExperienceCard(
          title: 'Traditional Drum Circle Workshop',
          location: 'Ikeja, Lagos',
          imageUrl: 'https://via.placeholder.com/400x300?text=Drum+Workshop',
          rating: 4.7,
          price: '\$30',
          duration: '2 hours',
          isHorizontal: true,
          category: 'Music',
          reviewCount: 45,
          onTap: () {
            // TODO: Navigate to experience details
          },
        ),
        const SizedBox(height: 16),
        ExperienceCard(
          title: 'Authentic Nigerian Cuisine Tour',
          location: 'Victoria Island, Lagos',
          imageUrl: 'https://via.placeholder.com/400x300?text=Cuisine+Tour',
          rating: 4.5,
          price: '\$40',
          duration: '3 hours',
          isHorizontal: true,
          category: 'Food',
          reviewCount: 78,
          onTap: () {
            // TODO: Navigate to experience details
          },
        ),
      ],
    );
  }
}
